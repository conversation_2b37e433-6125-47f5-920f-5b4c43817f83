贪吃蛇游戏 - 使用说明
====================

游戏简介：
这是一个经典的贪吃蛇游戏，使用易语言开发。玩家控制一条蛇在游戏区域内移动，吃食物来增长身体，同时避免撞墙或撞到自己的身体。

游戏规则：
1. 蛇会持续向当前方向移动
2. 吃到蓝色食物后，蛇身会增长一节，得分增加10分
3. 撞到墙壁或自己的身体时游戏结束
4. 蛇不能立即反向移动（例如向右移动时不能直接向左）

操作方法：
- 方向键：控制蛇的移动方向
  ↑ 上方向键：向上移动
  → 右方向键：向右移动
  ↓ 下方向键：向下移动
  ← 左方向键：向左移动
- 空格键：重新开始游戏
- 重新开始按钮：重置游戏
- 暂停/继续按钮：暂停或继续游戏

游戏界面：
- 白色区域：游戏区域
- 红色方块：蛇头
- 绿色方块：蛇身
- 蓝色方块：食物
- 灰色线条：网格线

技术特点：
- 使用易语言开发
- 实时绘图显示游戏画面
- 键盘响应控制
- 碰撞检测算法
- 随机食物生成
- 得分系统

文件说明：
- 贪吃蛇游戏.e：主程序源代码文件
- 贪吃蛇游戏.fne：窗口设计文件
- 游戏说明.txt：本说明文件

编译运行：
1. 使用易语言IDE打开"贪吃蛇游戏.e"文件
2. 按F5键或点击"编译"按钮进行编译
3. 编译成功后运行程序即可开始游戏

注意事项：
- 确保已安装易语言开发环境
- 游戏窗口大小固定，不可调整
- 游戏速度可通过修改时钟周期调整（默认200毫秒）
- 游戏区域为20x20格子

版本信息：
版本：1.0
开发工具：易语言
开发时间：2025年
作者：AI助手

祝您游戏愉快！

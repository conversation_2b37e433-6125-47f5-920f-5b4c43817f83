.版本 2

.程序集 窗口程序集1

.程序集变量 游戏宽度, 整数型
.程序集变量 游戏高度, 整数型
.程序集变量 方格大小, 整数型
.程序集变量 蛇身长度, 整数型
.程序集变量 游戏状态, 整数型
.程序集变量 得分, 整数型
.程序集变量 方向, 整数型
.程序集变量 蛇身X, 整数型, , "100"
.程序集变量 蛇身Y, 整数型, , "100"
.程序集变量 食物X, 整数型
.程序集变量 食物Y, 整数型

.子程序 _启动窗口_创建完毕

游戏初始化 ()

.子程序 游戏初始化

' 游戏参数设置
游戏宽度 = 20
游戏高度 = 20
方格大小 = 20
蛇身长度 = 3
游戏状态 = 1  ' 1=运行中 0=结束
得分 = 0
方向 = 2  ' 1=上 2=右 3=下 4=左

' 初始化蛇身位置
重定义数组 (蛇身X, 假, 100)
重定义数组 (蛇身Y, 假, 100)
蛇身X [1] = 5
蛇身Y [1] = 10
蛇身X [2] = 4
蛇身Y [2] = 10
蛇身X [3] = 3
蛇身Y [3] = 10

' 生成第一个食物
生成食物 ()

' 启动游戏定时器
时钟1.时钟周期 = 200
时钟1.禁止 = 假

.子程序 生成食物

.局部变量 随机X, 整数型
.局部变量 随机Y, 整数型
.局部变量 重复, 逻辑型
.局部变量 i, 整数型

重复 = 真
.判断循环首 (重复 = 真)
    随机X = 取随机数 (1, 游戏宽度)
    随机Y = 取随机数 (1, 游戏高度)
    重复 = 假
    
    ' 检查食物是否与蛇身重叠
    .计次循环首 (蛇身长度, i)
        .如果 (蛇身X [i] = 随机X 且 蛇身Y [i] = 随机Y)
            重复 = 真
            跳出循环
        .如果结束
    .计次循环尾 ()
.判断循环尾 ()

食物X = 随机X
食物Y = 随机Y

.子程序 _时钟1_周期事件

.如果 (游戏状态 = 0)
    返回 ()
.如果结束

移动蛇身 ()
检查碰撞 ()
检查食物 ()
绘制游戏 ()

.子程序 移动蛇身

.局部变量 i, 整数型
.局部变量 新头X, 整数型
.局部变量 新头Y, 整数型

' 计算新的蛇头位置
新头X = 蛇身X [1]
新头Y = 蛇身Y [1]

.判断开始 (方向 = 1)  ' 上
    新头Y = 新头Y - 1
.判断 (方向 = 2)  ' 右
    新头X = 新头X + 1
.判断 (方向 = 3)  ' 下
    新头Y = 新头Y + 1
.判断 (方向 = 4)  ' 左
    新头X = 新头X - 1
.默认

.判断结束

' 移动蛇身（从尾部开始）
.计次循环首 (蛇身长度 - 1, i)
    蛇身X [蛇身长度 - i + 1] = 蛇身X [蛇身长度 - i]
    蛇身Y [蛇身长度 - i + 1] = 蛇身Y [蛇身长度 - i]
.计次循环尾 ()

' 设置新的蛇头位置
蛇身X [1] = 新头X
蛇身Y [1] = 新头Y

.子程序 检查碰撞

' 检查是否撞墙
.如果 (蛇身X [1] < 1 或 蛇身X [1] > 游戏宽度 或 蛇身Y [1] < 1 或 蛇身Y [1] > 游戏高度)
    游戏结束 ()
    返回 ()
.如果结束

' 检查是否撞到自己
.局部变量 i, 整数型
.计次循环首 (蛇身长度 - 1, i)
    .如果 (蛇身X [1] = 蛇身X [i + 1] 且 蛇身Y [1] = 蛇身Y [i + 1])
        游戏结束 ()
        返回 ()
    .如果结束
.计次循环尾 ()

.子程序 检查食物

.如果 (蛇身X [1] = 食物X 且 蛇身Y [1] = 食物Y)
    ' 吃到食物，增加蛇身长度
    蛇身长度 = 蛇身长度 + 1
    得分 = 得分 + 10
    
    ' 生成新食物
    生成食物 ()
    
    ' 更新得分显示
    标签1.标题 = "得分：" + 到文本 (得分)
.如果结束

.子程序 绘制游戏

.局部变量 i, 整数型
.局部变量 画板, 整数型

画板 = 取设备场景 (画板1.取窗口句柄 ())

' 清空画板
画矩形 (画板, 0, 0, 游戏宽度 × 方格大小, 游戏高度 × 方格大小, #白色, #白色)

' 绘制蛇身
.计次循环首 (蛇身长度, i)
    .如果 (i = 1)
        ' 蛇头用红色
        画矩形 (画板, (蛇身X [i] - 1) × 方格大小, (蛇身Y [i] - 1) × 方格大小, 方格大小, 方格大小, #红色, #红色)
    .否则
        ' 蛇身用绿色
        画矩形 (画板, (蛇身X [i] - 1) × 方格大小, (蛇身Y [i] - 1) × 方格大小, 方格大小, 方格大小, #绿色, #绿色)
    .如果结束
.计次循环尾 ()

' 绘制食物（蓝色）
画矩形 (画板, (食物X - 1) × 方格大小, (食物Y - 1) × 方格大小, 方格大小, 方格大小, #蓝色, #蓝色)

' 绘制网格线
.局部变量 x, 整数型
.局部变量 y, 整数型
.计次循环首 (游戏宽度 + 1, x)
    画直线 (画板, (x - 1) × 方格大小, 0, (x - 1) × 方格大小, 游戏高度 × 方格大小, #灰色)
.计次循环尾 ()
.计次循环首 (游戏高度 + 1, y)
    画直线 (画板, 0, (y - 1) × 方格大小, 游戏宽度 × 方格大小, (y - 1) × 方格大小, #灰色)
.计次循环尾 ()

释放设备场景 (画板1.取窗口句柄 (), 画板)

.子程序 游戏结束

游戏状态 = 0
时钟1.禁止 = 真
信息框 ("游戏结束！\n最终得分：" + 到文本 (得分), 0, "贪吃蛇")

.子程序 _启动窗口_按键被按下, 逻辑型, , 键代码, 整数型, 功能键状态, 整数型

.如果 (游戏状态 = 0)
    返回 (假)
.如果结束

.判断开始 (键代码 = #上方向键)
    .如果 (方向 ≠ 3)  ' 不能反向
        方向 = 1
    .如果结束
.判断 (键代码 = #右方向键)
    .如果 (方向 ≠ 4)
        方向 = 2
    .如果结束
.判断 (键代码 = #下方向键)
    .如果 (方向 ≠ 1)
        方向 = 3
    .如果结束
.判断 (键代码 = #左方向键)
    .如果 (方向 ≠ 2)
        方向 = 4
    .如果结束
.判断 (键代码 = #空格键)
    ' 重新开始游戏
    游戏初始化 ()
.默认

.判断结束

返回 (真)

.子程序 _按钮1_被单击

' 重新开始按钮
游戏初始化 ()

.子程序 _按钮2_被单击

' 暂停/继续按钮
.如果 (时钟1.禁止 = 真)
    时钟1.禁止 = 假
    按钮2.标题 = "暂停"
.否则
    时钟1.禁止 = 真
    按钮2.标题 = "继续"
.如果结束
